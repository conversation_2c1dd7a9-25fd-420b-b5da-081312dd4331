defmodule Repobot.Files.RepoTreeTest do
  use Repobot.DataCase

  alias Repobot.Files.RepoTree
  import Repobot.Test.Fixtures

  # Move setup to module level
  setup do
    user = create_user()

    # Create repositories with and without files
    repo_with_files =
      create_repository(%{
        name: "repo1",
        owner: "owner1",
        full_name: "owner1/repo1",
        user_id: user.id
      })

    create_repository_file(%{
      repository_id: repo_with_files.id,
      path: "test.ex",
      content: "test content",
      size: 100
    })

    repo_without_files =
      create_repository(%{
        name: "repo2",
        owner: "owner1",
        full_name: "owner1/repo2",
        user_id: user.id
      })

    # Preload files
    repo_with_files = Repo.preload(repo_with_files, :files)
    repo_without_files = Repo.preload(repo_without_files, :files)

    {:ok, user: user, repo_with_files: repo_with_files, repo_without_files: repo_without_files}
  end

  describe "init_loading/1" do
    test "initializes loading states correctly", %{
      repo_with_files: repo_with_files,
      repo_without_files: repo_without_files
    } do
      {loading_states, needs_refresh} =
        RepoTree.init_loading([repo_with_files, repo_without_files])

      # Check loading states
      assert loading_states[repo_with_files.id] == :loaded
      assert loading_states[repo_without_files.id] == :loading

      # Check needs_refresh list
      assert length(needs_refresh) == 1
      assert hd(needs_refresh).id == repo_without_files.id
    end

    test "returns empty needs_refresh when all repos have files", %{
      repo_with_files: repo_with_files
    } do
      {loading_states, needs_refresh} = RepoTree.init_loading([repo_with_files])

      assert loading_states[repo_with_files.id] == :loaded
      assert Enum.empty?(needs_refresh)
    end
  end

  describe "load_trees/3" do
    test "starts loading trees for repositories", %{
      user: user,
      repo_without_files: repo_without_files
    } do
      # Stub GitHub client for this test
      Repobot.Test.GitHubMock
      |> Mox.stub(:client, fn _user -> %Tentacat.Client{} end)

      assert :ok = RepoTree.load_trees([repo_without_files], self(), user)
    end
  end

  describe "handle_tree_message/2" do
    test "handles tree_loaded message" do
      initial_states = %{"repo1" => :loading, "repo2" => :loaded}

      {states, all_loaded?} =
        RepoTree.handle_tree_message(initial_states, {:tree_loaded, "repo1"})

      assert states["repo1"] == :loaded
      assert states["repo2"] == :loaded
      assert all_loaded? == true
    end

    test "handles tree_load_failed message" do
      initial_states = %{"repo1" => :loading, "repo2" => :loaded}

      {states, all_loaded?} =
        RepoTree.handle_tree_message(initial_states, {:tree_load_failed, "repo1", "error"})

      assert match?({:error, "error"}, states["repo1"])
      assert states["repo2"] == :loaded
      assert all_loaded? == false
    end
  end

  describe "helper functions" do
    setup do
      states = %{
        "repo1" => :loaded,
        "repo2" => :loading,
        "repo3" => {:error, "failed"},
        "repo4" => :loaded
      }

      {:ok, states: states}
    end

    test "all_trees_loaded?/1", %{states: states} do
      refute RepoTree.all_trees_loaded?(states)

      loaded_states = %{"repo1" => :loaded, "repo2" => :loaded}
      assert RepoTree.all_trees_loaded?(loaded_states)
    end

    test "get_loading_state/2", %{states: states} do
      assert RepoTree.get_loading_state(states, "repo1") == :loaded
      assert RepoTree.get_loading_state(states, "repo2") == :loading
      assert RepoTree.get_loading_state(states, "unknown") == :not_started
    end

    test "loading?/1", %{states: states} do
      assert RepoTree.loading?(states)

      no_loading_states = %{"repo1" => :loaded, "repo2" => :loaded}
      refute RepoTree.loading?(no_loading_states)
    end

    test "failed_repos/1", %{states: states} do
      failed = RepoTree.failed_repos(states)
      assert failed == [{"repo3", "failed"}]
    end
  end

  describe "refresh_file_content/3" do
    test "refreshes content for all files with progress updates", %{
      user: user,
      repo_with_files: repo_with_files,
      repo_without_files: repo_without_files
    } do
      # Add a file to the second repo
      create_repository_file(%{
        repository_id: repo_without_files.id,
        path: "test2.ex",
        content: nil,
        size: 100
      })

      # Reload repositories
      repo_with_files = Repo.preload(repo_with_files, :files, force: true)
      repo_without_files = Repo.preload(repo_without_files, :files, force: true)

      # Stub GitHub client to return content
      Repobot.Test.GitHubMock
      |> Mox.stub(:client, fn _user -> %Tentacat.Client{} end)
      |> Mox.stub(:get_file_content, fn _client, _owner, _repo, path ->
        {:ok, "content for #{path}", %{}}
      end)

      assert :ok =
               RepoTree.refresh_file_content(
                 [repo_with_files, repo_without_files],
                 self(),
                 user
               )

      # Should receive progress updates and completion
      assert_receive {:content_refresh_progress, progress, _status} when progress > 0
      assert_receive {:content_refresh_complete, _refreshed_repos}
    end

    test "handles empty repository list", %{user: user} do
      assert :ok = RepoTree.refresh_file_content([], self(), user)
      assert_receive {:content_refresh_complete, _refreshed_repos}
    end

    test "handles GitHub API errors", %{
      user: user,
      repo_with_files: repo_with_files
    } do
      # Stub GitHub client to return error
      Repobot.Test.GitHubMock
      |> Mox.stub(:client, fn _user -> %Tentacat.Client{} end)
      |> Mox.stub(:get_file_content, fn _client, _owner, _repo, _path ->
        {:error, "API error"}
      end)

      assert :ok = RepoTree.refresh_file_content([repo_with_files], self(), user)
      assert_receive {:content_refresh_error, "API error"}
    end
  end
end
